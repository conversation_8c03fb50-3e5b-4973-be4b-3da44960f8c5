<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\DomainApiController;
use App\Http\Controllers\Api\SimpleDomainApiController;
use App\Http\Controllers\Api\CategoryApiController;
use App\Http\Controllers\Api\AuthApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthApiController::class, 'login']);
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthApiController::class, 'logout']);
        Route::get('/user', [AuthApiController::class, 'user']);
    });
});

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // Categories
    Route::apiResource('categories', CategoryApiController::class);
    
    // Domains
    Route::apiResource('domains', DomainApiController::class);
    Route::post('domains/check-availability', [DomainApiController::class, 'checkAvailability']);
    Route::post('domains/check-duplicate', [DomainApiController::class, 'checkDuplicate']);
    
    // Simple Domains
    Route::apiResource('simple-domains', SimpleDomainApiController::class);
    Route::post('simple-domains/check-duplicate', [SimpleDomainApiController::class, 'checkDuplicate']);
    Route::post('simple-domains/{simpleDomain}/buy', [SimpleDomainApiController::class, 'buy']);
    
    // Dashboard stats
    Route::get('dashboard/stats', [DomainApiController::class, 'getDashboardStats']);
});