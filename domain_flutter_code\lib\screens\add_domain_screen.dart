import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/domain_provider.dart';
import '../providers/category_provider.dart';
import '../utils/date_formatter.dart';

class AddDomainScreen extends StatefulWidget {
  const AddDomainScreen({super.key});

  @override
  State<AddDomainScreen> createState() => _AddDomainScreenState();
}

class _AddDomainScreenState extends State<AddDomainScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _customExtensionController = TextEditingController();
  DateTime _expiryDate = DateTime.now().add(const Duration(days: 365));
  int _rating = 5;
  List<int> _selectedCategories = [];
  
  // Extension management
  List<String> _selectedExtensions = ['.com']; // Default selection
  List<String> _customExtensions = [];
  final List<String> _commonExtensions = [
    '.com', '.in'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _customExtensionController.dispose();
    super.dispose();
  }

  void _toggleExtension(String extension) {
    setState(() {
      if (_selectedExtensions.contains(extension)) {
        _selectedExtensions.remove(extension);
      } else {
        _selectedExtensions.add(extension);
      }
    });
  }

  void _addCustomExtension() {
    final extension = _customExtensionController.text.trim();
    if (extension.isNotEmpty && !extension.startsWith('.')) {
      final formattedExtension = '.$extension';
      if (!_commonExtensions.contains(formattedExtension) && 
          !_customExtensions.contains(formattedExtension)) {
        setState(() {
          _customExtensions.add(formattedExtension);
          _selectedExtensions.add(formattedExtension);
          _customExtensionController.clear();
        });
      }
    } else if (extension.startsWith('.') && extension.length > 1) {
      if (!_commonExtensions.contains(extension) && 
          !_customExtensions.contains(extension)) {
        setState(() {
          _customExtensions.add(extension);
          _selectedExtensions.add(extension);
          _customExtensionController.clear();
        });
      }
    }
  }

  void _removeCustomExtension(String extension) {
    setState(() {
      _customExtensions.remove(extension);
      _selectedExtensions.remove(extension);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Domain'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Domain Name',
                  hintText: 'example',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a domain name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Extensions Section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Extensions',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  
                  // Common Extensions
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: _commonExtensions.map((extension) {
                      final isSelected = _selectedExtensions.contains(extension);
                      return FilterChip(
                        label: Text(extension),
                        selected: isSelected,
                        onSelected: (selected) => _toggleExtension(extension),
                        backgroundColor: Colors.grey.shade200,
                        selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                      );
                    }).toList(),
                  ),
                  
                  // Custom Extensions
                  if (_customExtensions.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: _customExtensions.map((extension) {
                        final isSelected = _selectedExtensions.contains(extension);
                        return FilterChip(
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(extension),
                              const SizedBox(width: 4),
                              GestureDetector(
                                onTap: () => _removeCustomExtension(extension),
                                child: const Icon(
                                  Icons.close,
                                  size: 16,
                                  color: Colors.red,
                                ),
                              ),
                            ],
                          ),
                          selected: isSelected,
                          onSelected: (selected) => _toggleExtension(extension),
                          backgroundColor: Colors.orange.shade100,
                          selectedColor: Colors.orange.shade300,
                        );
                      }).toList(),
                    ),
                  ],
                  
                  const SizedBox(height: 8),
                  
                  // Add Custom Extension
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _customExtensionController,
                          decoration: const InputDecoration(
                            hintText: 'Add custom extension (e.g., .xyz)',
                            border: OutlineInputBorder(),
                            isDense: true,
                          ),
                          onSubmitted: (_) => _addCustomExtension(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: _addCustomExtension,
                        child: const Text('Add'),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'Categories',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Consumer<CategoryProvider>(
                builder: (context, categoryProvider, child) {
                  if (categoryProvider.categories.isEmpty) {
                    return const Text('No categories available');
                  }

                  return Wrap(
                    spacing: 8,
                    children: categoryProvider.categories.map((category) {
                      final isSelected = _selectedCategories.contains(category.id);
                      return FilterChip(
                        label: Text(category.name),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedCategories.add(category.id);
                            } else {
                              _selectedCategories.remove(category.id);
                            }
                          });
                        },
                        backgroundColor: Color(int.parse(category.color.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.1),
                        selectedColor: Color(int.parse(category.color.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.3),
                      );
                    }).toList(),
                  );
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Expiry Date: '),
                  TextButton(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _expiryDate,
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 3650)),
                      );
                      if (date != null) {
                        setState(() {
                          _expiryDate = date;
                        });
                      }
                    },
                    child: Text(DateFormatter.formatDateFromDateTime(_expiryDate)),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'Rating',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: List.generate(5, (index) {
                  return IconButton(
                    icon: Icon(
                      Icons.star,
                      color: index < _rating ? Colors.amber : Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _rating = index + 1;
                      });
                    },
                  );
                }),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: Consumer<DomainProvider>(
                  builder: (context, domainProvider, child) {
                    return ElevatedButton(
                      onPressed: domainProvider.isLoading ? null : _saveDomain,
                      child: domainProvider.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Add Domain'),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveDomain() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedCategories.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least one category'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (_selectedExtensions.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least one extension'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final data = {
        'name': _nameController.text.trim(),
        'extensions': _selectedExtensions,
        'categories': _selectedCategories,
        'expiry_date': _expiryDate.toIso8601String().split('T')[0],
        'rating': _rating,
      };

      final domainProvider = Provider.of<DomainProvider>(context, listen: false);
      final success = await domainProvider.createDomain(data);

      if (mounted) {
        if (success) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Domain added successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(domainProvider.error ?? 'Failed to add domain'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}