import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/theme.dart';
import 'enhanced_button.dart';

enum EmptyStateType {
  noData,
  noResults,
  error,
  offline,
  maintenance,
  noPermission,
}

class EmptyState extends StatelessWidget {
  final EmptyStateType type;
  final String? title;
  final String? description;
  final IconData? icon;
  final String? actionText;
  final VoidCallback? onAction;
  final Widget? customIcon;
  final bool showAnimation;

  const EmptyState({
    super.key,
    required this.type,
    this.title,
    this.description,
    this.icon,
    this.actionText,
    this.onAction,
    this.customIcon,
    this.showAnimation = true,
  });

  const EmptyState.noData({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.actionText,
    this.onAction,
    this.customIcon,
    this.showAnimation = true,
  }) : type = EmptyStateType.noData;

  const EmptyState.noResults({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.actionText,
    this.onAction,
    this.customIcon,
    this.showAnimation = true,
  }) : type = EmptyStateType.noResults;

  const EmptyState.error({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.actionText,
    this.onAction,
    this.customIcon,
    this.showAnimation = true,
  }) : type = EmptyStateType.error;

  const EmptyState.offline({
    super.key,
    this.title,
    this.description,
    this.icon,
    this.actionText,
    this.onAction,
    this.customIcon,
    this.showAnimation = true,
  }) : type = EmptyStateType.offline;

  @override
  Widget build(BuildContext context) {
    final config = _getConfig();

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spaceLG),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            _buildIcon(config),
            const SizedBox(height: AppTheme.spaceLG),

            // Title
            Text(
              title ?? config.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppTheme.textPrimaryLight,
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spaceMD),

            // Description
            Text(
              description ?? config.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryLight,
                    height: 1.5,
                  ),
              textAlign: TextAlign.center,
            ),

            // Action button
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: AppTheme.spaceXL),
              EnhancedButton.primary(
                text: actionText!,
                onPressed: onAction,
                icon: config.actionIcon,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildIcon(EmptyStateConfig config) {
    Widget iconWidget;

    if (customIcon != null) {
      iconWidget = customIcon!;
    } else {
      iconWidget = Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          color: config.iconBackgroundColor.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon ?? config.icon,
          size: 60,
          color: config.iconColor,
        ),
      );
    }

    if (showAnimation) {
      return iconWidget
          .animate()
          .fadeIn(duration: 600.ms)
          .scale(begin: const Offset(0.8, 0.8), duration: 400.ms)
          .then(delay: 200.ms)
          .shimmer(duration: 1000.ms, color: config.iconColor.withValues(alpha: 0.3));
    }

    return iconWidget;
  }

  EmptyStateConfig _getConfig() {
    switch (type) {
      case EmptyStateType.noData:
        return EmptyStateConfig(
          title: 'No Data Available',
          description: 'There\'s no data to display at the moment. Try refreshing or check back later.',
          icon: Icons.inbox_outlined,
          iconColor: AppTheme.textSecondaryLight,
          iconBackgroundColor: AppTheme.textSecondaryLight,
          actionIcon: Icons.refresh,
        );

      case EmptyStateType.noResults:
        return EmptyStateConfig(
          title: 'No Results Found',
          description: 'We couldn\'t find any results matching your search. Try adjusting your filters or search terms.',
          icon: Icons.search_off_outlined,
          iconColor: AppTheme.textSecondaryLight,
          iconBackgroundColor: AppTheme.textSecondaryLight,
          actionIcon: Icons.clear,
        );

      case EmptyStateType.error:
        return EmptyStateConfig(
          title: 'Something Went Wrong',
          description: 'We encountered an error while loading your data. Please try again or contact support if the problem persists.',
          icon: Icons.error_outline,
          iconColor: AppTheme.errorLight,
          iconBackgroundColor: AppTheme.errorLight,
          actionIcon: Icons.refresh,
        );

      case EmptyStateType.offline:
        return EmptyStateConfig(
          title: 'You\'re Offline',
          description: 'Please check your internet connection and try again.',
          icon: Icons.wifi_off_outlined,
          iconColor: AppTheme.warningLight,
          iconBackgroundColor: AppTheme.warningLight,
          actionIcon: Icons.refresh,
        );

      case EmptyStateType.maintenance:
        return EmptyStateConfig(
          title: 'Under Maintenance',
          description: 'We\'re currently performing maintenance. Please check back in a few minutes.',
          icon: Icons.build_outlined,
          iconColor: AppTheme.infoLight,
          iconBackgroundColor: AppTheme.infoLight,
          actionIcon: Icons.refresh,
        );

      case EmptyStateType.noPermission:
        return EmptyStateConfig(
          title: 'Access Denied',
          description: 'You don\'t have permission to view this content. Please contact your administrator.',
          icon: Icons.lock_outline,
          iconColor: AppTheme.errorLight,
          iconBackgroundColor: AppTheme.errorLight,
          actionIcon: Icons.contact_support,
        );
    }
  }
}

class EmptyStateConfig {
  final String title;
  final String description;
  final IconData icon;
  final Color iconColor;
  final Color iconBackgroundColor;
  final IconData? actionIcon;

  const EmptyStateConfig({
    required this.title,
    required this.description,
    required this.icon,
    required this.iconColor,
    required this.iconBackgroundColor,
    this.actionIcon,
  });
}

// Specialized empty states for common use cases
class NoDomainsState extends StatelessWidget {
  final VoidCallback? onAddDomain;

  const NoDomainsState({
    super.key,
    this.onAddDomain,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyState.noData(
      title: 'No Domains Yet',
      description: 'Start building your domain portfolio by adding your first domain.',
      icon: Icons.domain_outlined,
      actionText: 'Add Domain',
      onAction: onAddDomain,
      customIcon: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.domain_outlined,
          size: 60,
          color: AppTheme.primaryLight,
        ),
      ),
    );
  }
}

class NoCategoriesState extends StatelessWidget {
  final VoidCallback? onAddCategory;

  const NoCategoriesState({
    super.key,
    this.onAddCategory,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyState.noData(
      title: 'No Categories Yet',
      description: 'Organize your domains better by creating categories.',
      icon: Icons.category_outlined,
      actionText: 'Add Category',
      onAction: onAddCategory,
      customIcon: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          gradient: AppTheme.secondaryGradient.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.category_outlined,
          size: 60,
          color: AppTheme.secondaryLight,
        ),
      ),
    );
  }
}

class SearchEmptyState extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;

  const SearchEmptyState({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyState.noResults(
      title: 'No Results for "$searchQuery"',
      description: 'Try searching with different keywords or check your spelling.',
      actionText: 'Clear Search',
      onAction: onClearSearch,
    );
  }
}
