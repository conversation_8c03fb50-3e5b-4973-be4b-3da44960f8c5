import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/domain_provider.dart';
import '../providers/category_provider.dart';

class AddSimpleDomainScreen extends StatefulWidget {
  const AddSimpleDomainScreen({super.key});

  @override
  State<AddSimpleDomainScreen> createState() => _AddSimpleDomainScreenState();
}

class _AddSimpleDomainScreenState extends State<AddSimpleDomainScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  List<int> _selectedCategories = [];

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reserve Domain'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Domain Name',
                  hintText: 'example',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a domain name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'Categories',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Consumer<CategoryProvider>(
                builder: (context, categoryProvider, child) {
                  if (categoryProvider.categories.isEmpty) {
                    return const Text('No categories available');
                  }

                  return Wrap(
                    spacing: 8,
                    children: categoryProvider.categories.map((category) {
                      final isSelected = _selectedCategories.contains(category.id);
                      return FilterChip(
                        label: Text(category.name),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedCategories.add(category.id);
                            } else {
                              _selectedCategories.remove(category.id);
                            }
                          });
                        },
                        backgroundColor: Color(int.parse(category.color.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.1),
                        selectedColor: Color(int.parse(category.color.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.3),
                      );
                    }).toList(),
                  );
                },
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: Consumer<DomainProvider>(
                  builder: (context, domainProvider, child) {
                    return ElevatedButton(
                      onPressed: domainProvider.isLoading ? null : _saveSimpleDomain,
                      child: domainProvider.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Reserve Domain'),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveSimpleDomain() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedCategories.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least one category'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final data = {
        'name': _nameController.text.trim(),
        'categories': _selectedCategories,
      };

      final domainProvider = Provider.of<DomainProvider>(context, listen: false);
      final success = await domainProvider.createSimpleDomain(data);

      if (mounted) {
        if (success) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Domain reserved successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(domainProvider.error ?? 'Failed to reserve domain'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}