import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/admin.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';
import 'dart:convert';

class AuthProvider with ChangeNotifier {
  Admin? _admin;
  bool _isLoading = false;
  String? _error;

  Admin? get admin => _admin;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _admin != null;

  Future<void> checkAuthStatus() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.authToken);
      final userData = prefs.getString(StorageKeys.userData);

      if (token != null && userData != null) {
        _admin = Admin.fromJson(json.decode(userData));
        
        // Verify token is still valid
        try {
          await ApiService.getUser();
        } catch (e) {
          // Token is invalid, clear stored data
          await logout();
        }
      }
    } catch (e) {
      _setError('Failed to check authentication status');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      print('AuthProvider: Starting login process for $email');
      
      // Validate input
      if (email.trim().isEmpty || password.isEmpty) {
        _setError('Email and password are required');
        return false;
      }

      // Validate email format
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email.trim())) {
        _setError('Please enter a valid email address');
        return false;
      }

      final response = await ApiService.login(email.trim(), password);
      print('AuthProvider: Login response received: $response');
      
      // Check if response contains required fields
      if (response.containsKey('success') && response['success'] == true) {
        final token = response['token'];
        final adminData = response['admin'];
        
        if (token == null || adminData == null) {
          _setError('Invalid response from server - missing token or admin data');
          return false;
        }
        
        try {
          _admin = Admin.fromJson(adminData);
          print('AuthProvider: Admin object created successfully: ${_admin?.name}');
        } catch (e) {
          print('AuthProvider: Error creating admin object: $e');
          print('AuthProvider: Admin data received: $adminData');
          _setError('Invalid user data received from server');
          return false;
        }
        
        // Store token and user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(StorageKeys.authToken, token);
        await prefs.setString(StorageKeys.userData, json.encode(adminData));
        
        print('AuthProvider: Login successful, data stored');
        notifyListeners();
        return true;
      } else {
        // Handle case where success is not true or missing
        final message = response['message'] ?? 'Login failed - invalid credentials';
        _setError(message);
        return false;
      }
    } catch (e) {
      print('AuthProvider: Login error: $e');
      String errorMessage = e.toString();
      
      // Clean up error message
      if (errorMessage.startsWith('Exception: ')) {
        errorMessage = errorMessage.substring(11);
      }
      
      // Provide more user-friendly error messages
      if (errorMessage.contains('SocketException') || errorMessage.contains('No internet connection')) {
        errorMessage = 'No internet connection. Please check your network.';
      } else if (errorMessage.contains('TimeoutException') || errorMessage.contains('timeout')) {
        errorMessage = 'Connection timeout. Please try again.';
      } else if (errorMessage.contains('FormatException')) {
        errorMessage = 'Server response error. Please try again.';
      } else if (errorMessage.contains('401') || errorMessage.contains('Unauthorized')) {
        errorMessage = 'Invalid email or password.';
      } else if (errorMessage.contains('422')) {
        errorMessage = 'Invalid credentials provided.';
      } else if (errorMessage.contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      }
      
      _setError(errorMessage);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    
    try {
      if (_admin != null) {
        await ApiService.logout();
      }
    } catch (e) {
      // Continue with logout even if API call fails
    }

    // Clear stored data
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(StorageKeys.authToken);
    await prefs.remove(StorageKeys.userData);
    
    _admin = null;
    _setLoading(false);
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}