import 'dart:convert';
import 'package:http/http.dart' as http;
import 'constants.dart';

class DebugHelper {
  static Future<void> testApiConnection() async {
    print('=== Testing API Connection ===');
    
    try {
      // Test basic connectivity
      final uri = Uri.parse('${ApiConstants.baseUrl}/auth/login');
      print('Testing connection to: $uri');
      
      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'email': '<EMAIL>',
          'password': 'wrongpassword',
        }),
      ).timeout(const Duration(seconds: 10));
      
      print('Response Status: ${response.statusCode}');
      print('Response Headers: ${response.headers}');
      print('Response Body: ${response.body}');
      
      if (response.statusCode == 422) {
        print('✅ API is reachable (validation error expected)');
      } else if (response.statusCode == 404) {
        print('❌ API endpoint not found - check your Laravel routes');
      } else if (response.statusCode == 500) {
        print('❌ Server error - check your Laravel logs');
      } else {
        print('ℹ️ Unexpected response code: ${response.statusCode}');
      }
      
    } catch (e) {
      print('❌ Connection failed: $e');
      
      if (e.toString().contains('SocketException')) {
        print('💡 Suggestions:');
        print('   - Make sure Laravel server is running (php artisan serve)');
        print('   - Check if the base URL is correct');
        print('   - For Android emulator, try http://********:8000/api');
        print('   - For physical device, use your computer\'s IP address');
      }
    }
    
    print('==============================');
  }
  
  static Future<void> testLoginWithCredentials(String email, String password) async {
    print('=== Testing Login with Credentials ===');
    print('Email: $email');
    
    try {
      final uri = Uri.parse('${ApiConstants.baseUrl}/auth/login');
      final response = await http.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'email': email,
          'password': password,
        }),
      ).timeout(const Duration(seconds: 10));
      
      print('Response Status: ${response.statusCode}');
      print('Response Body: ${response.body}');
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          print('✅ Login successful!');
          print('Token received: ${data['token'] != null ? 'Yes' : 'No'}');
          print('Admin data received: ${data['admin'] != null ? 'Yes' : 'No'}');
        } else {
          print('❌ Login failed: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        print('❌ Login failed with status: ${response.statusCode}');
      }
      
    } catch (e) {
      print('❌ Login test failed: $e');
    }
    
    print('=====================================');
  }
}